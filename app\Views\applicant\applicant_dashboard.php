<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-2">Welcome, <?= esc($applicant['first_name']) ?> <?= esc($applicant['last_name']) ?></h1>
                            <p class="text-muted mb-0">Manage your applications and profile</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary" onclick="location.href='<?= base_url('applicant/profile') ?>'">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Access Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-bolt me-2 text-primary"></i>Quick Access
                    </h5>
                    <div class="row g-3">
                        <!-- Dashboard -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/dashboard') ?>" class="card text-decoration-none hover-card h-100 <?= ($menu == 'dashboard') ? 'border-primary' : '' ?>">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-home text-primary fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">Dashboard</div>
                                </div>
                            </a>
                        </div>

                        <!-- Job Openings -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/jobs') ?>" class="card text-decoration-none hover-card h-100 <?= ($menu == 'jobs') ? 'border-primary' : '' ?>">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-briefcase text-success fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">Job Openings</div>
                                </div>
                            </a>
                        </div>

                        <!-- Applications -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/applications') ?>" class="card text-decoration-none hover-card h-100 <?= ($menu == 'applications') ? 'border-primary' : '' ?>">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-file-alt text-info fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">My Applications</div>
                                </div>
                            </a>
                        </div>

                        <!-- Profile -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/profile') ?>" class="card text-decoration-none hover-card h-100 <?= ($menu == 'profile') ? 'border-primary' : '' ?>">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-user text-warning fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">Profile</div>
                                </div>
                            </a>
                        </div>

                        <!-- Upload Documents -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/profile/files/create') ?>" class="card text-decoration-none hover-card h-100">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-upload text-secondary fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">Upload Files</div>
                                </div>
                            </a>
                        </div>

                        <!-- Change Password -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('applicant/profile') ?>#password" class="card text-decoration-none hover-card h-100">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-key text-danger fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">Change Password</div>
                                </div>
                            </a>
                        </div>

                        <!-- Download RS 3.2 Form -->
                        <div class="col-6 col-sm-4 col-md-3 col-lg-2">
                            <a href="<?= base_url('public/assets/Form_RS_3.2.docx') ?>" class="card text-decoration-none hover-card h-100" download="Form_RS_3.2.docx">
                                <div class="card-body text-center p-3">
                                    <i class="fas fa-download text-purple fs-4 mb-2 quick-access-icon"></i>
                                    <div class="small fw-medium text-dark">RS 3.2 Form</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Forms Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">
                        <i class="fas fa-file-download me-2 text-primary"></i>Application Forms
                    </h5>
                    <div class="row g-3">
                        <div class="col-md-6 col-lg-4">
                            <div class="card border border-primary">
                                <div class="card-body text-center p-4">
                                    <div class="mb-3">
                                        <i class="fas fa-file-word text-primary" style="font-size: 3rem;"></i>
                                    </div>
                                    <h6 class="card-title">RS 3.2 Application Form</h6>
                                    <p class="card-text text-muted small mb-3">
                                        Official application form required for all job applications. Download, fill out, and upload with your application.
                                    </p>
                                    <a href="<?= base_url('public/assets/Form_RS_3.2.docx') ?>"
                                       class="btn btn-primary btn-sm"
                                       download="Form_RS_3.2.docx">
                                        <i class="fas fa-download me-2"></i>Download Form
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-lg-8">
                            <div class="alert alert-info mb-0">
                                <h6 class="alert-heading">
                                    <i class="fas fa-info-circle me-2"></i>Important Instructions
                                </h6>
                                <ul class="mb-0 small">
                                    <li>Download the RS 3.2 Application Form before applying for any position</li>
                                    <li>Fill out all required fields completely and accurately</li>
                                    <li>Save the completed form and upload it when submitting your application</li>
                                    <li>Ensure all information matches your profile details</li>
                                    <li>The form is required for all job applications in the system</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Total Applications</h6>
                            <h3 class="mb-0"><?= $total_applications ?></h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-file-alt text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Pending</h6>
                            <h3 class="mb-0"><?= $pending_applications ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-clock text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Shortlisted</h6>
                            <h3 class="mb-0"><?= $shortlisted_applications ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Rejected</h6>
                            <h3 class="mb-0"><?= $rejected_applications ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-times-circle text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Completion Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">Profile Completion</h5>
                    <div class="progress mb-3" style="height: 10px;">
                        <?php
                        $completion = 0;
                        $total_requirements = 6; // 5 profile fields + 1 file upload requirement
                        $completed_requirements = 0;

                        // Check profile fields (5 requirements, each worth 16.67% for total of 83.33%)
                        $fields = ['gender', 'dobirth', 'place_of_origin', 'contact_details', 'location_address'];
                        foreach ($fields as $field) {
                            if (!empty($applicant[$field])) {
                                $completed_requirements++;
                            }
                        }

                        // Check file upload requirement (1 requirement worth 16.67%)
                        $has_files = isset($files_count) && $files_count > 0;
                        if ($has_files) {
                            $completed_requirements++;
                        }

                        // Calculate completion percentage
                        $completion = round(($completed_requirements / $total_requirements) * 100);
                        ?>
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $completion ?>%"
                             aria-valuenow="<?= $completion ?>" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>

                    <!-- Profile Completion Details -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">Profile Requirements:</h6>
                            <ul class="list-unstyled small">
                                <li class="<?= !empty($applicant['gender']) ? 'text-success' : 'text-muted' ?>">
                                    <i class="fas fa-<?= !empty($applicant['gender']) ? 'check-circle' : 'circle' ?> me-2"></i>
                                    Gender Information
                                </li>
                                <li class="<?= !empty($applicant['dobirth']) ? 'text-success' : 'text-muted' ?>">
                                    <i class="fas fa-<?= !empty($applicant['dobirth']) ? 'check-circle' : 'circle' ?> me-2"></i>
                                    Date of Birth
                                </li>
                                <li class="<?= !empty($applicant['place_of_origin']) ? 'text-success' : 'text-muted' ?>">
                                    <i class="fas fa-<?= !empty($applicant['place_of_origin']) ? 'check-circle' : 'circle' ?> me-2"></i>
                                    Place of Origin
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-2">&nbsp;</h6>
                            <ul class="list-unstyled small">
                                <li class="<?= !empty($applicant['contact_details']) ? 'text-success' : 'text-muted' ?>">
                                    <i class="fas fa-<?= !empty($applicant['contact_details']) ? 'check-circle' : 'circle' ?> me-2"></i>
                                    Contact Details
                                </li>
                                <li class="<?= !empty($applicant['location_address']) ? 'text-success' : 'text-muted' ?>">
                                    <i class="fas fa-<?= !empty($applicant['location_address']) ? 'check-circle' : 'circle' ?> me-2"></i>
                                    Address Information
                                </li>
                                <li class="<?= $has_files ? 'text-success' : 'text-danger' ?>">
                                    <i class="fas fa-<?= $has_files ? 'check-circle' : 'exclamation-circle' ?> me-2"></i>
                                    At least one file uploaded <?= $has_files ? "({$files_count} files)" : "(Required)" ?>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <p class="text-muted mb-0">
                        Your profile is <?= $completion ?>% complete (<?= $completed_requirements ?>/<?= $total_requirements ?> requirements met).
                        <?php if ($completion < 100): ?>
                            <?php if (!$has_files): ?>
                                <strong class="text-danger">You must upload at least one file to complete your profile.</strong>
                                <a href="<?= base_url('applicant/profile/files/create') ?>" class="btn btn-sm btn-danger ms-2">
                                    <i class="fas fa-upload me-1"></i>Upload Files
                                </a>
                            <?php else: ?>
                                <a href="<?= base_url('applicant/profile') ?>">Complete your profile</a> to increase your chances of getting hired.
                            <?php endif; ?>
                        <?php else: ?>
                            <span class="text-success"><i class="fas fa-check-circle me-1"></i>Profile completed! You can now apply for positions.</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Applications -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title mb-0">Recent Applications</h5>
                        <a href="<?= base_url('applicant/applications') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Applied Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recent_applications ?? [])): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">No applications found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($recent_applications as $application): ?>
                                    <tr>
                                        <td><?= esc($application['position_title']) ?></td>
                                        <td><?= esc($application['department']) ?></td>
                                        <td><?= date('M d, Y', strtotime($application['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-<?= get_status_color($application['application_status']) ?>">
                                                <?= esc($application['application_status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('applicant/application/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Job Openings -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title mb-0">Latest Job Openings</h5>
                        <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <?php if (empty($latest_jobs ?? [])): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No job openings available at the moment</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($latest_jobs as $job): ?>
                            <a href="<?= base_url('applicant/jobs/view/' . $job['id']) ?>" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= esc($job['title']) ?></h6>
                                    <small class="text-muted"><?= time_elapsed_string($job['posted_date']) ?></small>
                                </div>
                                <p class="mb-1 text-muted small"><?= esc($job['department']) ?></p>
                                <small class="text-primary">
                                    <i class="fas fa-map-marker-alt me-1"></i><?= esc($job['location']) ?>
                                </small>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('styles') ?>
<style>
.hover-card {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.hover-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border-color: #007bff !important;
    border-width: 2px !important;
}

.hover-card:hover .text-dark {
    color: #007bff !important;
}

.quick-access-icon {
    transition: all 0.3s ease;
}

.hover-card:hover .quick-access-icon {
    transform: scale(1.1);
}

.text-purple {
    color: #6f42c1 !important;
}

/* Active/Current page card styling */
.hover-card.border-primary {
    border-color: #007bff !important;
    border-width: 2px !important;
    background-color: #f8f9ff;
}

.hover-card.border-primary .text-dark {
    color: #007bff !important;
}

/* Ensure all quick access cards have visible borders */
.card.hover-card {
    border: 1px solid #dee2e6 !important;
    background-color: #ffffff;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any dashboard-specific JavaScript here
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 