<?= $this->extend('templates/nolstemp') ?>
<?= $this->section('content') ?>
<div class="container mt-4">
    <h3>Position Details</h3>
    <div class="card mb-4">
        <div class="card-body">
            <div class="row mb-2">
                <div class="col-md-6"><strong>Position Reference:</strong> <?= esc($position['position_reference']) ?></div>
                <div class="col-md-6"><strong>Designation:</strong> <?= esc($position['designation']) ?></div>
            </div>
            <div class="row mb-2">
                <div class="col-md-6"><strong>Classification:</strong> <?= esc($position['classification']) ?></div>
                <div class="col-md-6"><strong>Award:</strong> <?= esc($position['award']) ?></div>
            </div>
            <div class="row mb-2">
                <div class="col-md-6"><strong>Location:</strong> <?= esc($position['location']) ?></div>
                <div class="col-md-6"><strong>Annual Salary:</strong> <?= esc($position['annual_salary']) ?></div>
            </div>
            <div class="row mb-2">
                <div class="col-md-6"><strong>Status:</strong> <?= esc($position['status']) ?></div>
                <div class="col-md-6">
                    <strong>Job Description:</strong>
                    <?php if (!empty($position['jd_filepath'])): ?>
                        <a href="<?= base_url($position['jd_filepath']) ?>" target="_blank">Download</a>
                    <?php else: ?>
                        <span class="text-muted">No JD</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="mb-2"><strong>Qualifications:</strong><br><?= nl2br(esc($position['qualifications'])) ?></div>
            <div class="mb-2"><strong>Knowledge:</strong><br><?= nl2br(esc($position['knowledge'])) ?></div>
            <div class="mb-2"><strong>Skills & Competencies:</strong><br><?= nl2br(esc($position['skills_competencies'])) ?></div>
            <div class="mb-2"><strong>Job Experiences:</strong><br><?= nl2br(esc($position['job_experiences'])) ?></div>
            <div class="mb-2"><strong>Remarks:</strong><br><?= nl2br(esc($position['remarks'])) ?></div>
        </div>
    </div>

    <!-- AI Extracted JDS Texts Section -->
    <?php if (!empty($position['jd_texts_extracted'])): ?>
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-robot me-2"></i>AI Extracted Job Description Content
                </h5>
                <button class="btn btn-sm btn-outline-light" type="button" data-bs-toggle="collapse" data-bs-target="#extractedTextCollapse" aria-expanded="false" aria-controls="extractedTextCollapse">
                    <i class="fas fa-eye me-1"></i>View Extracted Content
                </button>
            </div>
        </div>
        <div class="collapse" id="extractedTextCollapse">
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    This content was automatically extracted from the uploaded Job Description PDF using AI technology.
                </div>

                <div id="extractedContentDisplay">
                    <!-- Content will be populated by JavaScript -->
                </div>

                <!-- Raw JSON View (Collapsible) -->
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#rawJsonCollapse" aria-expanded="false" aria-controls="rawJsonCollapse">
                        <i class="fas fa-code me-1"></i>View Raw JSON Data
                    </button>
                    <div class="collapse mt-2" id="rawJsonCollapse">
                        <div class="card card-body bg-light">
                            <pre id="rawJsonContent" style="max-height: 400px; overflow-y: auto; white-space: pre-wrap; font-size: 11px; font-family: 'Courier New', monospace;"><?= esc($position['jd_texts_extracted']) ?></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <a href="<?= base_url('positions/view_positions/' . esc($position['position_group_id'])) ?>" class="btn btn-secondary">Back to Positions List</a>
</div>
<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.page-section {
    border-left: 4px solid #007bff;
    padding-left: 15px;
    margin-bottom: 20px;
}

.profile-section {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
}

.original-text-section {
    background-color: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
}

.page-section h5 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.profile-section h6 {
    color: #6c757d;
    margin-bottom: 10px;
}

.original-text-section h6 {
    color: #495057;
    margin-bottom: 10px;
}

.list-unstyled li {
    margin-bottom: 5px;
    padding: 2px 0;
}

.original-text-content {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #212529;
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    white-space: pre-wrap;
    font-size: 0.95em;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.no-content-message {
    color: #6c757d;
    font-style: italic;
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 5px;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Process and display extracted content when the collapse is shown
    $('#extractedTextCollapse').on('show.bs.collapse', function () {
        displayExtractedContent();
    });
});

function displayExtractedContent() {
    const extractedText = <?= json_encode($position['jd_texts_extracted'] ?? '') ?>;
    const displayContainer = document.getElementById('extractedContentDisplay');

    if (!extractedText || extractedText.trim() === '') {
        displayContainer.innerHTML = '<div class="no-content-message">No extracted content available.</div>';
        return;
    }

    try {
        // Try to parse the JSON content
        const jsonData = JSON.parse(extractedText);
        let formattedDisplay = '';

        if (jsonData.pages && Array.isArray(jsonData.pages)) {
            // Display each page with proper formatting
            jsonData.pages.forEach((page, index) => {
                formattedDisplay += `<div class="page-section">`;
                formattedDisplay += `<h5><i class="fas fa-file-alt me-2"></i>Page ${page.page_number || (index + 1)}</h5>`;

                // Display page profile if available
                if (page.profile) {
                    formattedDisplay += `<div class="profile-section">`;
                    formattedDisplay += `<h6><i class="fas fa-info-circle me-2"></i>Page Profile</h6>`;
                    formattedDisplay += `<ul class="list-unstyled">`;

                    if (page.profile.content_type) {
                        formattedDisplay += `<li><strong><i class="fas fa-tag me-1"></i>Content Type:</strong> ${escapeHtml(page.profile.content_type)}</li>`;
                    }
                    if (page.profile.key_information) {
                        formattedDisplay += `<li><strong><i class="fas fa-key me-1"></i>Key Information:</strong> ${escapeHtml(page.profile.key_information)}</li>`;
                    }
                    if (page.profile.main_sections && Array.isArray(page.profile.main_sections)) {
                        formattedDisplay += `<li><strong><i class="fas fa-list me-1"></i>Main Sections:</strong> ${page.profile.main_sections.map(section => escapeHtml(section)).join(', ')}</li>`;
                    }
                    if (page.profile.document_elements && Array.isArray(page.profile.document_elements)) {
                        formattedDisplay += `<li><strong><i class="fas fa-puzzle-piece me-1"></i>Document Elements:</strong> ${page.profile.document_elements.map(element => escapeHtml(element)).join(', ')}</li>`;
                    }
                    if (page.profile.layout_description) {
                        formattedDisplay += `<li><strong><i class="fas fa-layout me-1"></i>Layout:</strong> ${escapeHtml(page.profile.layout_description)}</li>`;
                    }

                    formattedDisplay += `</ul></div>`;
                }

                // Display original text if available
                if (page.original_text) {
                    formattedDisplay += `<div class="original-text-section">`;
                    formattedDisplay += `<h6><i class="fas fa-file-text me-2"></i>Original Text Content</h6>`;
                    formattedDisplay += `<div class="original-text-content">${escapeHtml(page.original_text)}</div>`;
                    formattedDisplay += `</div>`;
                }

                // Display details if available (alternative to original_text)
                if (page.details && !page.original_text) {
                    formattedDisplay += `<div class="original-text-section">`;
                    formattedDisplay += `<h6><i class="fas fa-file-text me-2"></i>Detailed Content</h6>`;
                    if (typeof page.details === 'object') {
                        formattedDisplay += `<div class="original-text-content">${JSON.stringify(page.details, null, 2)}</div>`;
                    } else {
                        formattedDisplay += `<div class="original-text-content">${escapeHtml(page.details)}</div>`;
                    }
                    formattedDisplay += `</div>`;
                }

                formattedDisplay += `</div>`;

                // Add separator between pages (except for the last page)
                if (index < jsonData.pages.length - 1) {
                    formattedDisplay += `<hr class="my-4">`;
                }
            });
        } else if (jsonData.error) {
            // Handle error in JSON
            formattedDisplay = `<div class="error-message">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Processing Error:</strong> ${escapeHtml(jsonData.error)}
            </div>`;
        } else {
            // Handle other JSON structures
            formattedDisplay = `<div class="original-text-content">${escapeHtml(JSON.stringify(jsonData, null, 2))}</div>`;
        }

        displayContainer.innerHTML = formattedDisplay;

    } catch (error) {
        // If JSON parsing fails, display as plain text
        console.warn('Failed to parse JSON, displaying as plain text:', error);
        displayContainer.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Note:</strong> Content could not be parsed as JSON. Displaying as plain text.
            </div>
            <div class="original-text-content">${escapeHtml(extractedText)}</div>
        `;
    }
}

function escapeHtml(text) {
    if (typeof text !== 'string') {
        text = String(text);
    }
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
<?= $this->endSection() ?>